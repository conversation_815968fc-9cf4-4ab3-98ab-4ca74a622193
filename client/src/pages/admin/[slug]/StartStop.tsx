import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search, Plus, X, Loader2, Calendar } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { useESealData, type ESealData } from '../../../hooks/useESealData';
import MapTiler from '../../../components/map/MapTiler';
import '../../../components/map/MapTiler.css';

const StartTrackingModal = ({ isOpen, onClose, onSubmit, eseals, loading }: any) => {
  const [formData, setFormData] = useState({
    noEseal: '',
    alamatAsal: '',
    alamatTujuan: '',
    latitudeAsal: '',
    longitudeAsal: '',
    latitudeTujuan: '',
    longitudeTujuan: '',
    noKontainer: '',
    ukKontainer: '',
    jnsKontainer: '',
    noPolisi: '',
    namaDriver: '',
    nomorTeleponDriver: '',
    nomorAju: '',
    tanggalBerangkat: '',
  });

  const [originLocation, setOriginLocation] = useState<{ lat: number; lng: number; address?: string } | undefined>();
  const [destinationLocation, setDestinationLocation] = useState<{ lat: number; lng: number; address?: string } | undefined>();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleOriginSelect = (location: { lat: number; lng: number; address?: string }) => {
    setOriginLocation(location);
    setFormData({
      ...formData,
      alamatAsal: location.address || '',
      latitudeAsal: location.lat.toString(),
      longitudeAsal: location.lng.toString(),
    });
  };

  const handleDestinationSelect = (location: { lat: number; lng: number; address?: string }) => {
    setDestinationLocation(location);
    setFormData({
      ...formData,
      alamatTujuan: location.address || '',
      latitudeTujuan: location.lat.toString(),
      longitudeTujuan: location.lng.toString(),
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl mx-4 max-h-[95vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Mulai Tracking Baru</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Top Row - Origin and Destination */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                placeholder="Surabaya"
                value={formData.alamatAsal}
                onChange={(e) => setFormData({ ...formData, alamatAsal: e.target.value })}
              />
              <Input
                placeholder="Banten"
                value={formData.alamatTujuan}
                onChange={(e) => setFormData({ ...formData, alamatTujuan: e.target.value })}
              />
              <Select onValueChange={(v) => setFormData({ ...formData, jnsKontainer: v })}>
                <SelectTrigger>
                  <SelectValue placeholder="GENERAL / DRY CARGO" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GENERAL">GENERAL</SelectItem>
                  <SelectItem value="DRY_CARGO">DRY CARGO</SelectItem>
                  <SelectItem value="REEFER">REEFER</SelectItem>
                  <SelectItem value="TANK">TANK</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Maps Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Origin Map */}
              <div className="space-y-2">
                <div className="h-48 rounded-lg overflow-hidden border border-gray-300">
                  <MapTiler
                    originLocation={originLocation}
                    onOriginSelect={handleOriginSelect}
                    height="100%"
                    interactive={true}
                  />
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="ASAL/DARI"
                    className="text-xs"
                    value={formData.alamatAsal}
                    onChange={(e) => setFormData({ ...formData, alamatAsal: e.target.value })}
                  />
                  <Input
                    placeholder="TUJUAN/KE"
                    className="text-xs"
                    value={formData.alamatTujuan}
                    onChange={(e) => setFormData({ ...formData, alamatTujuan: e.target.value })}
                  />
                </div>
              </div>

              {/* Destination Map */}
              <div className="space-y-2">
                <div className="h-48 rounded-lg overflow-hidden border border-gray-300">
                  <MapTiler
                    destinationLocation={destinationLocation}
                    onDestinationSelect={handleDestinationSelect}
                    height="100%"
                    interactive={true}
                  />
                </div>
                <Input
                  placeholder="Kendal, Jawa Tengah"
                  className="text-xs"
                  value={destinationLocation?.address || ''}
                  readOnly
                />
              </div>
            </div>

            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder="Jakarta Utara, DKI Jakarta"
                value={originLocation?.address || ''}
                readOnly
              />
              <Input
                placeholder="7854658292"
                value={formData.nomorTeleponDriver}
                onChange={(e) => setFormData({ ...formData, nomorTeleponDriver: e.target.value })}
              />
              <Input
                placeholder="473"
                value={formData.ukKontainer}
                onChange={(e) => setFormData({ ...formData, ukKontainer: e.target.value })}
              />
              <Input
                placeholder="AE 547 GA"
                value={formData.noPolisi}
                onChange={(e) => setFormData({ ...formData, noPolisi: e.target.value })}
              />
              <Input
                placeholder="12567788"
                value={formData.noKontainer}
                onChange={(e) => setFormData({ ...formData, noKontainer: e.target.value })}
              />

              <Select onValueChange={(v) => setFormData({ ...formData, ukKontainer: v })}>
                <SelectTrigger>
                  <SelectValue placeholder="40 FEET" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="20_FEET">20 FEET</SelectItem>
                  <SelectItem value="40_FEET">40 FEET</SelectItem>
                  <SelectItem value="45_FEET">45 FEET</SelectItem>
                </SelectContent>
              </Select>

              <Input
                placeholder="Budi Surya"
                value={formData.namaDriver}
                onChange={(e) => setFormData({ ...formData, namaDriver: e.target.value })}
              />
              <Input
                placeholder="08792429262624"
                value={formData.nomorAju}
                onChange={(e) => setFormData({ ...formData, nomorAju: e.target.value })}
              />

              {/* E-Seal Selection */}
              <Select onValueChange={(v) => setFormData({ ...formData, noEseal: v })}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih E-Seal" />
                </SelectTrigger>
                <SelectContent>
                  {eseals.map((eseal: ESealData) => (
                    <SelectItem key={eseal.id} value={eseal.noEseal!}>
                      {eseal.noEseal} ({eseal.noImei})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Input
                placeholder="Muat Ekspor"
                value={formData.namaDriver}
                onChange={(e) => setFormData({ ...formData, namaDriver: e.target.value })}
              />
              <Input
                placeholder="7375632252588"
                className="text-xs"
              />
              <Input
                placeholder="123"
                className="text-xs"
              />
              <Input
                placeholder="123"
                className="text-xs"
              />
              <Input
                placeholder="457"
                className="text-xs"
              />
            </div>

            {/* Date Picker */}
            <div className="flex items-center gap-2">
              <Input
                type="date"
                value={formData.tanggalBerangkat}
                onChange={(e) => setFormData({ ...formData, tanggalBerangkat: e.target.value })}
                className="max-w-xs"
              />
              <Calendar className="w-4 h-4 text-gray-400" />
            </div>

            {/* Required Fields Note */}
            <p className="text-xs text-gray-500">* Kolom wajib diisi</p>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button type="button" variant="ghost" onClick={onClose}>
                Batal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Mulai Tracking
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};


const StartStop: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [stoppingEsealId, setStoppingEsealId] = useState<string | null>(null);

  const { data: esealData, loading, error, refetch } = useESealData({
    page: 1,
    limit: 100, // Fetch all for selection
    organizationSlug: slug,
  });

  const activeEseals = esealData?.filter(e => e.status === 'ACTIVE') || [];
  const inactiveEseals = esealData?.filter(e => e.status === 'INACTIVE') || [];

  const handleStartTracking = async (formData: any) => {
    setIsSubmitting(true);
    const selectedEseal = esealData?.find(e => e.noEseal === formData.noEseal);
    if (!selectedEseal) {
      alert('E-Seal tidak ditemukan');
      setIsSubmitting(false);
      return;
    }

    const payload = {
      ...formData,
      noImei: selectedEseal.noImei,
      idVendor: selectedEseal.idVendor,
      token: '', // Add token if needed
      organizationSlug: slug,
    };

    try {
      const response = await fetch('/api/beacukai/tracking/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const result = await response.json();
      if (!response.ok) throw new Error(result.message || 'Gagal memulai tracking');
      alert('Tracking berhasil dimulai');
      setIsModalOpen(false);
      refetch();
    } catch (err: any) {
      alert(`Error: ${err.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStopTracking = async (eseal: ESealData) => {
    const alamat = prompt(`Masukkan alamat pemberhentian untuk E-Seal ${eseal.noEseal}:`);
    if (!alamat) {
      alert('Alamat pemberhentian tidak boleh kosong.');
      return;
    }

    setStoppingEsealId(eseal.id);

    const payload = {
      noEseal: eseal.noEseal,
      noImei: eseal.noImei,
      idVendor: eseal.idVendor,
      alamatStop: alamat,
      latitudeStop: '0', // Placeholder, ideally get from browser GPS
      longitudeStop: '0', // Placeholder, ideally get from browser GPS
      token: '', // TODO: figure out where to get the token
      organizationSlug: slug,
    };

    try {
      const response = await fetch('/api/beacukai/tracking/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const result = await response.json();
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Gagal menghentikan tracking');
      }
      alert('Tracking berhasil dihentikan');
      refetch();
    } catch (err: any) {
      alert(`Terjadi kesalahan: ${err.message}`);
    } finally {
      setStoppingEsealId(null);
    }
  };


  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Tracking Data - Start/Stop</h1>
      </div>

      <div className="flex justify-end items-center gap-3 bg-white p-4 rounded-lg border">
        <Button onClick={() => setIsModalOpen(true)} className="bg-slate-800 hover:bg-slate-700 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Mulai Tracking
        </Button>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">Nomor E-Seal</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor AJU</TableHead>
                <TableHead className="font-semibold text-slate-700">Status</TableHead>
                <TableHead className="font-semibold text-slate-700">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8"><Loader2 className="mx-auto h-6 w-6 animate-spin" /></TableCell></TableRow>
              ) : error ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8 text-red-500">{error}</TableCell></TableRow>
              ) : activeEseals.length === 0 ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8 text-gray-500">Tidak ada tracking yang aktif.</TableCell></TableRow>
              ) : (
                activeEseals.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.noEseal}</TableCell>
                    <TableCell>{item.noImei}</TableCell>
                    <TableCell>{item.nomorAJU || '-'}</TableCell>
                    <TableCell><span className="text-green-600 font-semibold">Aktif</span></TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleStopTracking(item)}
                        disabled={stoppingEsealId === item.id}
                      >
                        {stoppingEsealId === item.id ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          'Stop'
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      <StartTrackingModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleStartTracking}
        eseals={inactiveEseals}
        loading={isSubmitting}
      />
    </div>
  );
};

export default StartStop;
