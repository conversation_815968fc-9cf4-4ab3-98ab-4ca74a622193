import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Search, Eye, Plus, X, Loader2, Edit } from 'lucide-react';
import SetupESeal from './SetupESeal';
import DetailESeal from './DetailESeal';
import { useESealData, type ESealData } from '../../hooks/useESealData';
import { useOrganizations } from '../../hooks/useOrganizations';
import { VENDOR_OPTIONS, ESEAL_TYPES } from '@shared';



interface ESealFormData {
  organizationId: string;
  idVendor: string;
  merk: string;
  model: string;
  nomorIMEI: string;
  tipeESeal: string;
  token: string;
  gpsDeviceId: string;
}

interface GpsDevice {
  vehicleId: number;
  vehicleName: string;
  deviceId: string;
  groupName: string;
  online: boolean;
  lat: number;
  lng: number;
  updateTime: string;
}



// Modal Component
const TambahDataModal = ({
  isOpen,
  onClose,
  onSubmit,
  editData,
  isEdit = false,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ESealFormData) => void;
  editData?: ESealData;
  isEdit?: boolean;
}) => {
  const { organizations, loading: orgLoading } = useOrganizations();

  const [formData, setFormData] = useState<ESealFormData>({
    organizationId: '',
    idVendor: '',
    merk: '',
    model: '',
    nomorIMEI: '',
    tipeESeal: '',
    token: '',
    gpsDeviceId: '',
  });

  const [gpsDevices, setGpsDevices] = useState<GpsDevice[]>([]);
  const [loadingGpsDevices, setLoadingGpsDevices] = useState(false);

  // Populate form data when editing
  useEffect(() => {
    if (isEdit && editData) {
      setFormData({
        organizationId: editData.organizationId || '',
        idVendor: editData.idVendor || '',
        merk: editData.merk || '',
        model: editData.model || '',
        nomorIMEI: editData.noImei || '',
        tipeESeal: editData.tipe || '',
        token: '', // Don't populate token for security
        gpsDeviceId: (editData as any).gpsDeviceId || '',
      });
    } else {
      // Reset form when not editing
      setFormData({
        organizationId: '',
        idVendor: '',
        merk: '',
        model: '',
        nomorIMEI: '',
        tipeESeal: '',
        token: '',
        gpsDeviceId: '',
      });
    }
  }, [isEdit, editData, isOpen]);

  // Fetch GPS devices when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchGpsDevices();
    }
  }, [isOpen]);

  const fetchGpsDevices = async () => {
    setLoadingGpsDevices(true);
    try {
      console.log('📍 Fetching GPS devices...');
      const response = await fetch('/api/beacukai/gps/devices');
      const result = await response.json();

      if (response.ok && result.success) {
        setGpsDevices(result.data);
        console.log('✅ GPS devices loaded:', result.data.length);
      } else {
        console.error('❌ Failed to fetch GPS devices:', result.error);
        setGpsDevices([]);
      }
    } catch (error) {
      console.error('❌ Error fetching GPS devices:', error);
      setGpsDevices([]);
    } finally {
      setLoadingGpsDevices(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    // Don't reset form or close modal here - let parent handle it
  };

  const handleCancel = () => {
    setFormData({
      organizationId: '',
      idVendor: '',
      merk: '',
      model: '',
      nomorIMEI: '',
      tipeESeal: '',
      token: '',
      gpsDeviceId: '',
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">
              {isEdit ? 'Edit Data E-Seal' : 'Tambah Data E-Seal'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Organization */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Organisasi <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.organizationId}
                onValueChange={(value) => setFormData({...formData, organizationId: value})}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={orgLoading ? "Memuat organisasi..." : "Pilih organisasi"} />
                </SelectTrigger>
                <SelectContent>
                  {organizations.map((org) => (
                    <SelectItem key={org.id} value={org.id}>
                      {org.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* ID Vendor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ID Vendor <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.idVendor}
                onValueChange={(value) => setFormData({...formData, idVendor: value})}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pilih vendor" />
                </SelectTrigger>
                <SelectContent>
                  {VENDOR_OPTIONS.map((vendor) => (
                    <SelectItem key={vendor.value} value={vendor.value}>
                      {vendor.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Merk */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Merk <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan merk E-Seal"
                value={formData.merk}
                onChange={(e) => setFormData({...formData, merk: e.target.value})}
                required
              />
            </div>

            {/* Model */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Model <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan model E-Seal"
                value={formData.model}
                onChange={(e) => setFormData({...formData, model: e.target.value})}
                required
              />
            </div>

            {/* Nomor IMEI */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor IMEI <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan nomor IMEI"
                value={formData.nomorIMEI}
                onChange={(e) => setFormData({...formData, nomorIMEI: e.target.value})}
                required
              />
            </div>

            {/* Tipe */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipe <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.tipeESeal}
                onValueChange={(value) => setFormData({...formData, tipeESeal: value})}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pilih tipe" />
                </SelectTrigger>
                <SelectContent>
                  {ESEAL_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* GPS Device */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                GPS Device <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.gpsDeviceId}
                onValueChange={(value) => setFormData({ ...formData, gpsDeviceId: value })}
                required
                disabled={loadingGpsDevices}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={loadingGpsDevices ? "Loading GPS devices..." : "Pilih GPS Device"} />
                </SelectTrigger>
                <SelectContent>
                  {gpsDevices.map((device) => (
                    <SelectItem key={device.deviceId} value={device.deviceId}>
                      <div className="flex flex-col">
                        <span className="font-medium">{device.vehicleName}</span>
                        <span className="text-xs text-gray-500">
                          ID: {device.deviceId} | {device.online ? '🟢 Online' : '🔴 Offline'} |
                          Last: {new Date(device.updateTime).toLocaleString('id-ID')}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                  {gpsDevices.length === 0 && !loadingGpsDevices && (
                    <SelectItem value="" disabled>
                      Tidak ada GPS device tersedia
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">
                💡 Pilih GPS device yang akan digunakan untuk tracking lokasi E-Seal ini
              </p>
            </div>

            {/* Token */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Token <span className="text-gray-500">(opsional)</span>
              </label>
              <Input
                placeholder="Masukkan token jika diperlukan"
                value={formData.token}
                onChange={(e) => setFormData({...formData, token: e.target.value})}
              />
            </div>

            {/* Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                className="flex-1"
              >
                Batal
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-slate-800 hover:bg-slate-700 text-white"
              >
                {isEdit ? 'Update' : 'Simpan'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default function DataESeal() {
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState('10');
  const [currentPage, setCurrentPage] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showSetup, setShowSetup] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [selectedESeal, setSelectedESeal] = useState<ESealData | null>(null);
  const [editingESeal, setEditingESeal] = useState<ESealData | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
      setCurrentPage(1); // Reset to first page when searching
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Use real API data
  const { data: esealData, total, loading, error, refetch } = useESealData({
    page: currentPage,
    limit: parseInt(entriesPerPage),
    search: debouncedSearch,
  });


  const [showStopModal, setShowStopModal] = useState(false);
  const [esealToStop, setESealToStop] = useState<ESealData | null>(null);
  const [stopLocation, setStopLocation] = useState('');
  const [isStopping, setIsStopping] = useState(false);
  const [lastKnownPosition, setLastKnownPosition] = useState<{
    latitude: string;
    longitude: string;
    address: string;
    available: boolean;
  } | null>(null);

  // Use real data from API (filtering is handled by backend)
  const currentData = esealData || [];
  const totalPages = Math.ceil(total / parseInt(entriesPerPage));

  const handleDetail = (id: string) => {
    console.log('View detail for:', id);
    const eseal = esealData?.find(item => item.id === id);
    if (eseal) {
      setSelectedESeal(eseal);
      setShowDetail(true);
    }
  };

  const confirmStopMonitoring = async () => {
    if (!esealToStop) return;

    setIsStopping(true);
    try {
      // Try to get last known position from server first
      let latitudeStop = '0';
      let longitudeStop = '0';
      let alamatStop = stopLocation.trim();
      let positionSource = 'manual';

      try {
        console.log('📍 Getting last known position for E-Seal:', esealToStop.id);
        const positionResponse = await fetch(`/api/beacukai/tracking/last-position/${esealToStop.id}`);
        const positionData = await positionResponse.json();

        if (positionResponse.ok && positionData.success &&
            positionData.data.latitude !== '0' && positionData.data.longitude !== '0') {
          latitudeStop = positionData.data.latitude;
          longitudeStop = positionData.data.longitude;
          positionSource = 'database';

          // Use last known address if user didn't provide one
          if (!alamatStop) {
            alamatStop = positionData.data.address;
          }

          console.log('📍 Using last known position from database:', { latitudeStop, longitudeStop, alamatStop });
        } else {
          throw new Error('No valid database position available');
        }
      } catch (dbError) {
        // Fallback to browser geolocation
        console.log('📍 Database position not available, trying browser GPS');
        try {
          const position = await new Promise<GeolocationPosition | null>((resolve) => {
            if (navigator.geolocation) {
              navigator.geolocation.getCurrentPosition(
                (pos) => resolve(pos),
                (err) => {
                  console.warn(`Could not get browser geolocation: ${err.message}`);
                  resolve(null);
                },
                { timeout: 5000, enableHighAccuracy: true }
              );
            } else {
              resolve(null);
            }
          });

          if (position) {
            latitudeStop = position.coords.latitude.toString();
            longitudeStop = position.coords.longitude.toString();
            positionSource = 'browser';

            // Use GPS coordinates as address if user didn't provide one
            if (!alamatStop) {
              alamatStop = `GPS: ${latitudeStop}, ${longitudeStop}`;
            }

            console.log('📍 Using browser geolocation:', { latitudeStop, longitudeStop });
          }
        } catch (gpsError) {
          console.warn('⚠️ Browser GPS also failed');
        }
      }

      // Ensure we have an address
      if (!alamatStop) {
        alamatStop = 'Posisi pemberhentian tidak diketahui';
      }

      console.log(`📍 Final stop data - Source: ${positionSource}, Address: ${alamatStop}, Coordinates: ${latitudeStop}, ${longitudeStop}`);

      // 1. Call Beacukai tracking/stop API
      const stopPayload = {
        alamatStop,
        idVendor: esealToStop.idVendor,
        latitudeStop,
        longitudeStop,
        noImei: esealToStop.noImei,
        noEseal: esealToStop.noEseal,
        token: '919253c8-d0e1-4780-89d0-e91f77e89855',
      };

      const stopResponse = await fetch('/api/beacukai/tracking/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(stopPayload),
      });

      const stopResult = await stopResponse.json();
      if (!stopResponse.ok || !stopResult.success) {
        throw new Error(stopResult.message || 'Gagal menghentikan tracking di sistem Beacukai.');
      }
      console.log('✅ Tracking stopped successfully on Beacukai API');

      // 2. Update local E-Seal status to INACTIVE
      const updateResponse = await fetch(`/api/eseal/${esealToStop.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'INACTIVE' }),
      });

      if (!updateResponse.ok) {
        throw new Error('Gagal memperbarui status E-Seal lokal.');
      }

      alert('🎉 Monitoring E-Seal berhasil dihentikan.');
      refetch(); // Refresh data
    } catch (error) {
      console.error('❌ Error stopping monitoring:', error);
      alert(`Terjadi kesalahan: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsStopping(false);
      setShowStopModal(false);
      setESealToStop(null);
      setStopLocation('');
      setLastKnownPosition(null);
    }
  };

  const cancelStopMonitoring = () => {
    setShowStopModal(false);
    setESealToStop(null);
    setStopLocation('');
    setLastKnownPosition(null);
  };

  const handleKelola = async (id: string, status: string) => {
    console.log('Kelola action:', status, 'for:', id);

    if (status === 'INACTIVE') {
      // Find the selected E-Seal data
      const eseal = esealData?.find(item => item.id === id);
      if (eseal) {
        setSelectedESeal(eseal);
        setShowSetup(true);
      }
    } else if (status === 'ACTIVE') {
      // Show confirmation modal for stopping monitoring
      const eseal = esealData?.find(item => item.id === id);
      if (eseal) {
        setESealToStop(eseal);

        // Try to get last known position to pre-fill the modal
        try {
          console.log('📍 Pre-loading last known position for modal...');
          const positionResponse = await fetch(`/api/beacukai/tracking/last-position/${id}`);
          const positionData = await positionResponse.json();

          if (positionResponse.ok && positionData.success &&
              positionData.data.latitude !== '0' && positionData.data.longitude !== '0') {
            setLastKnownPosition({
              latitude: positionData.data.latitude,
              longitude: positionData.data.longitude,
              address: positionData.data.address,
              available: true
            });
            setStopLocation(positionData.data.address);
            console.log('📍 Pre-filled stop location:', positionData.data.address);
          } else {
            setLastKnownPosition({
              latitude: '0',
              longitude: '0',
              address: 'Posisi tidak tersedia',
              available: false
            });
          }
        } catch (error) {
          console.warn('⚠️ Could not pre-load position for modal');
          setLastKnownPosition({
            latitude: '0',
            longitude: '0',
            address: 'Gagal mengambil posisi',
            available: false
          });
        }

        setShowStopModal(true);
      }
    }
  };



  const handleAddData = async (formData: ESealFormData) => {
    console.log(`📝 ${isEditMode ? 'Editing' : 'Adding new'} data:`, formData);
    console.log(`🔍 GPS Device ID from form:`, formData.gpsDeviceId);

    const requestBody = {
      organizationId: formData.organizationId,
      idVendor: formData.idVendor,
      merk: formData.merk,
      model: formData.model,
      noImei: formData.nomorIMEI,
      tipeESeal: formData.tipeESeal,
      token: formData.token || "",
      gpsDeviceId: formData.gpsDeviceId || null // GPS Device ID for tracking
    };

    try {
      let response;
      let successMessage;

      if (isEditMode && editingESeal) {
        // Edit mode
        console.log('🌐 Request URL: /api/eseal/' + editingESeal.id);
        console.log('📦 Request Body:', requestBody);

        response = await fetch(`/api/eseal/${editingESeal.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });
        successMessage = 'E-Seal berhasil diupdate!';
      } else {
        // Add mode
        console.log('🌐 Request URL: /api/beacukai/eseal/create');
        console.log('📦 Request Body:', requestBody);

        response = await fetch('/api/beacukai/eseal/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });
      }

      console.log('📡 Response Status:', response.status, response.statusText);
      console.log('📡 Response Headers:', Object.fromEntries(response.headers.entries()));

      const responseText = await response.text();
      console.log('📄 Raw Response:', responseText);

      let result;
      try {
        result = JSON.parse(responseText);
        console.log('📄 Parsed Response:', result);
      } catch (parseError) {
        console.error('❌ Failed to parse JSON response:', parseError);
        throw new Error(`Invalid JSON response: ${responseText}`);
      }

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      if (result.success) {
        console.log(`✅ E-Seal ${isEditMode ? 'updated' : 'created'} successfully:`, result.data);

        if (isEditMode) {
          alert(successMessage);
        } else {
          // Show appropriate message based on local save status for new E-Seals
          if (result.data.local === 'skipped') {
            alert('E-Seal created in Beacukai system but could not be saved locally. Please check the system.');
          } else if (result.data.local === 'duplicate') {
            alert('E-Seal created in Beacukai system. This IMEI already exists in local database.');
          } else {
            alert('E-Seal created successfully in both Beacukai system and local database!');
          }
        }

        // Refresh data and close modal
        refetch();
        setIsModalOpen(false);
        setIsEditMode(false);
        setEditingESeal(null);
      } else {
        throw new Error(result.error || `Failed to ${isEditMode ? 'update' : 'create'} E-Seal`);
      }
    } catch (error) {
      console.error(`❌ Error ${isEditMode ? 'updating' : 'creating'} E-Seal:`, error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleEditESeal = (eseal: ESealData) => {
    setEditingESeal(eseal);
    setIsEditMode(true);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setIsEditMode(false);
    setEditingESeal(null);
  };

  // Show detail page if detail is active
  if (showDetail && selectedESeal) {
    return (
      <DetailESeal
        esealId={selectedESeal.id}
        onBack={() => {
          setShowDetail(false);
          setSelectedESeal(null);
          refetch();
        }}
      />
    );
  }

  // Show setup page if setup is active
  console.log('🔍 Render check - showSetup:', showSetup, 'selectedESeal:', selectedESeal?.id);

  if (showSetup && selectedESeal) {
    console.log('📱 Rendering SetupESeal component');
    return (
      <SetupESeal
        esealData={{
          id: selectedESeal.id,
          nama: selectedESeal.noEseal,
          nomorIMEI: selectedESeal.noImei,
          merk: selectedESeal.merk,
          model: selectedESeal.model,
          tipe: selectedESeal.tipe,
          idVendor: selectedESeal.idVendor
        }}
        onBack={() => {
          console.log('🔙 onBack called in DataESeal, hiding setup...');
          setShowSetup(false);
          setSelectedESeal(null);
          console.log('🔄 Refreshing data...');
          // Refresh data when coming back
          refetch();
          console.log('✅ Back to DataESeal page');
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Data E-Seal</h1>
        <p className="text-gray-600">Kelola dan pantau data E-Seal untuk setiap pengiriman.</p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white p-4 rounded-lg border">
        {/* Show entries */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Show</span>
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-600">entries per page</span>
        </div>

        {/* Search and Add Button */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Cari data..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-48 sm:w-64"
            />
          </div>
          <Button
            onClick={() => setIsModalOpen(true)}
            className="bg-slate-800 hover:bg-slate-700 text-white"
          >
            <Plus className="w-4 h-4 sm:mr-2" />
            <span className="hidden sm:inline">Tambah Data</span>
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">Nama</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor AJU</TableHead>
                <TableHead className="font-semibold text-slate-700">Organisasi</TableHead>
                <TableHead className="font-semibold text-slate-700">Status</TableHead>
                <TableHead className="font-semibold text-slate-700">Kelola</TableHead>
                <TableHead className="font-semibold text-slate-700">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <Loader2 className="w-6 h-6 animate-spin mr-2" />
                      <span className="text-gray-500">Memuat data...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-red-500">
                    <div className="flex flex-col items-center">
                      <span className="mb-2">Error: {error}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => refetch()}
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        Coba Lagi
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : currentData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data masih kosong'}
                  </TableCell>
                </TableRow>
              ) : (
                currentData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.noEseal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noImei}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorAJU || '-'}</TableCell>
                    <TableCell className="text-sm">
                      {item.organization ? (
                        <span className="text-blue-600 font-medium">{item.organization.name}</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={item.status === 'ACTIVE' ? 'default' : 'secondary'}
                        className={item.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                      >
                        {item.status === 'ACTIVE' ? 'Aktif' : 'Tidak Aktif'}
                      </Badge>
                    </TableCell>

                    <TableCell>
                      <Button
                        size="sm"
                        variant={item.status === 'ACTIVE' ? 'destructive' : 'default'}
                        className={item.status === 'ACTIVE'
                          ? 'bg-red-100 text-red-800 border border-red-200 hover:bg-red-200'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                        }
                        onClick={() => handleKelola(item.id, item.status)}
                        disabled={false}
                      >
                        {item.status === 'ACTIVE' ? 'Berhenti' : 'Mulai'}
                      </Button>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDetail(item.id)}
                          className="text-slate-600 hover:text-slate-800"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          Lihat
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEditESeal(item)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Edit className="w-4 h-4 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination info */}
      {total > 0 && (
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            Showing {((currentPage - 1) * parseInt(entriesPerPage)) + 1} to {Math.min(currentPage * parseInt(entriesPerPage), total)} of {total} entries
          </span>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">
              {currentPage}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modal */}
      <TambahDataModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleAddData}
        editData={editingESeal || undefined}
        isEdit={isEditMode}
      />

      {/* Stop Monitoring Confirmation Modal */}
      {showStopModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Konfirmasi Berhenti Monitoring</h3>
              <button onClick={cancelStopMonitoring} className="text-gray-400 hover:text-gray-600">
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6">
              <p className="text-gray-600 text-sm mb-4">
                Anda akan menghentikan monitoring untuk E-Seal berikut. Sistem akan menggunakan posisi terakhir yang tercatat.
              </p>
              {esealToStop && (
                <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
                  <div className="font-medium text-gray-900">{esealToStop.noEseal}</div>
                  <div className="text-sm text-gray-600">IMEI: {esealToStop.noImei}</div>
                </div>
              )}

              {/* Display Last Known Position */}
              {lastKnownPosition && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center mb-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-blue-900">
                      {lastKnownPosition.available ? 'Posisi Terakhir Tersimpan' : 'Status Posisi'}
                    </span>
                  </div>
                  <div className="text-sm text-blue-800">
                    <div className="mb-1">📍 <strong>Alamat:</strong> {lastKnownPosition.address}</div>
                    {lastKnownPosition.available && (
                      <div className="text-xs text-blue-600">
                        📐 Koordinat: {lastKnownPosition.latitude}, {lastKnownPosition.longitude}
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div>
                <label htmlFor="stopLocation" className="block text-sm font-medium text-gray-700 mb-2">
                  Alamat Berhenti <span className="text-gray-500">(opsional)</span>
                </label>
                <Input
                  id="stopLocation"
                  placeholder={lastKnownPosition?.available
                    ? "Kosongkan untuk menggunakan posisi di atas, atau ubah alamat"
                    : "Masukkan alamat pemberhentian"
                  }
                  value={stopLocation}
                  onChange={(e) => setStopLocation(e.target.value)}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {lastKnownPosition?.available
                    ? "💡 Akan menggunakan posisi terakhir di atas jika alamat tidak diubah"
                    : "⚠️ Posisi terakhir tidak tersedia, mohon masukkan alamat pemberhentian"
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center justify-end gap-3 p-6 bg-gray-50 border-t">
              <Button variant="outline" onClick={cancelStopMonitoring} disabled={isStopping}>
                Batal
              </Button>
              <Button
                onClick={confirmStopMonitoring}
                className="bg-red-600 hover:bg-red-700 text-white"
                disabled={isStopping}
              >
                {isStopping ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Menghentikan...
                  </>
                ) : (
                  'Ya, Hentikan'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
